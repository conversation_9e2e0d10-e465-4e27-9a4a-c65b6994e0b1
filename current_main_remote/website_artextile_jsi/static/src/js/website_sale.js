/** @odoo-module **/
import wSaleUtils from "@website_sale/js/website_sale_utils";
import { WebsiteSale } from "@website_sale/js/website_sale";
import { Component } from "@odoo/owl";

import { cookie } from "@web/core/browser/cookie";
import { _t } from "@web/core/l10n/translation";
import { sprintf } from "@web/core/utils/strings";
import { useService } from "@web/core/utils/hooks";
import { WarningDialog } from "@web/core/errors/error_dialogs";

import { ConfirmationDialog } from "@web/core/confirmation_dialog/confirmation_dialog";
import {
  DeliveryLeadTimeReminderDialog,
  IGNORE_REMINDER_TODAY_COOKIE,
} from "@website_artextile_jsi/delivery_lead_time_reminder_dialog/delivery_lead_time_reminder_dialog";

WebsiteSale.include({
  events: Object.assign({}, WebsiteSale.prototype.events, {
    "change #uom_selector": "_onChangeUom_selector",
    "change input.line_remark[data-product-id]": "_onChangelineRemark",
    "change input.line_customer_po[data-product-id]": "_onChangeLineCustomerPO",
    "focusout input.quantity": "_onInputQuantityKeyEvent",
    "keypress input.quantity": "_onInputQuantityKeyEvent",
    "click a[name='website_sale_main_button']": "_onClickCheckout",
  }),

  /**
   * Call onChangeVariant when we change the UOM in `Product Page`
   */
  _onChangeUom_selector: function (ev) {
    this.onChangeVariant(ev);
  },

  /**
   * Helper method to write Remark field in sale order line
   * @private
   * @param {Event} ev
   */
  _onChangelineRemark: function (ev) {
    const $input = $(ev.currentTarget);
    const line_id = parseInt($input.data("line-id"));
    this.rpc("/shop/cart/update_line_remark", {
      product_id: parseInt($input.data("product-id"), 10),
      line_id: line_id,
      line_remark: $input.val(),
    });
  },

  /**
   * Helper method to write LB Rmks field in sale order line
   * @private
   * @param {Event} ev
   */
  _onChangeLineCustomerPO: function (ev) {
    const $input = $(ev.currentTarget);
    const line_id = parseInt($input.data("line-id"));
    this.rpc("/shop/cart/update_line_customer_po", {
      product_id: parseInt($input.data("product-id"), 10),
      line_id: line_id,
      line_customer_po: $input.val(),
    });
  },

  /**
   * Before this method, we was doing this: (qty / 0.125 !== parseInt(qty / 0.125) && qty / 0.05 !== parseInt(qty / 0.05)
   * Here goal is ordered quantity should always be multiplier of 0.125 and 0.05
   * BUT if we order 1.2 qty and apply above condition, 1.2 / 0.05 >>> it returns 23.99999
   * So, we need to do the round it first before further checking
   * @private
   */
  _checkOrderedQty: function (qty) {
    const res1 = Math.round((qty / 0.125) * 100) / 100;
    const res2 = Math.round((qty / 0.05) * 100) / 100;
    return res1 !== parseInt(res1) && res2 !== parseInt(res2);
  },

  /**
   * This will triggered from `Product Page` and `Cart Line`
   * When we enter Quantity manually in input tag, we need to check the Quantity rules
   * Need to check from, TAB(FOCUSOUT), ENTER, or click on Add to cart after entering qty
   * No need to check it on every keypress, only for ENTER is enough
   */
  _onInputQuantityKeyEvent: function (ev) {
    if (
      (ev.type === "keypress" && ev.keyCode === 13) ||
      ev.type === "focusout"
    ) {
      const $input = $(ev.currentTarget);
      console.log($input.val());
      const qty = parseFloat($input.val()).toFixed(3);
      const max_allowed_qty = parseFloat($input.data("max-allowed-qty"));
      if (qty > max_allowed_qty) {
        this.raiseWarningForCustomCodition(ev, max_allowed_qty);
        return false;
      } else if (this._checkOrderedQty(qty)) {
        this.raiseWarningForCustomCodition(ev);
        return false;
      }
    }
  },

  /**
   * @private
   * Prepare pop-up for warning
   */
  raiseWarningForCustomCodition: function (ev, max_allowed_qty) {
    const $input = $(ev.currentTarget);
    $input.val("1.000");
    ev.stopImmediatePropagation();
    ev.preventDefault();
    $(this).off("click");
    const title = _t("Quantity Validation failed");
    let message = _t(
      "Wrong quantity! Please enter a multiple of 0.05 or 0.125"
    );
    if (max_allowed_qty) {
      message = sprintf(
        _t("To order more than %s of these items, please reach us."),
        max_allowed_qty
      );
    }
    this.call("dialog", "add", ConfirmationDialog, {
      title: title,
      body: message,
    });
  },

  /**
   * Update the _updateRootProduct to get the uom and added it in the rootProduct to be used in cart_update
   * @override
   */
  _updateRootProduct($form, productId) {
    this._super(...arguments);
    this.rootProduct.selected_uom = $form.find("select#uom_selector").val();
  },

  /**
   * Update the _getOptionalCombinationInfoParam to get the uom and returned inside the CombinationData in _getCombinationInfo
   * @override
   */
  _getOptionalCombinationInfoParam($product) {
    try {
      const uom = $product.find("select#uom_selector").val();
      return { selected_uom: uom };
    } catch (error) {
      return {};
    }
  },

  /**
   * This called when we change quantity by click on "+ -" buttons
   * Need to convert quantity to float
   * @override
   */
  _onChangeCartQuantity: function (ev) {
    var $input = $(ev.currentTarget);
    if ($input.data("update_change")) {
      return;
    }
    var value = parseFloat($input.val() || 0, 10);
    if (isNaN(value)) {
      value = 1;
    }
    var $dom = $input.closest("tr");
    // var default_price = parseFloat($dom.find('.text-danger > span.oe_currency_value').text());
    var $dom_optional = $dom.nextUntil(":not(.optional_product.info)");
    var line_id = parseInt($input.data("line-id"), 10);
    var productIDs = [parseInt($input.data("product-id"), 10)];
    this._changeCartQuantity($input, value, $dom_optional, line_id, productIDs);
  },

  /**
   * 1) Convert Quantity with 3 decimal point
   * 2) Hack to add and remove from cart with json
   * 3) Check the max_allowed_qty and raise warning when user increased from + button on Product Page and cart line
   * @override
   */
  onClickAddCartJSON: function (ev) {
    ev.preventDefault();
    var $link = $(ev.currentTarget);
    var $input = $link.closest(".input-group").find("input");
    var min = parseFloat($input.data("min") || 0);
    var max = parseFloat($input.data("max") || Infinity);
    var previousQty = parseFloat($input.val() || 0, 10);
    var quantity = parseFloat(
      ($link.has(".fa-minus").length ? -1 : 1) + previousQty
    ).toFixed(3);
    var newQty = quantity > min ? (quantity < max ? quantity : max) : min;
    const max_allowed_qty = parseFloat($input.data("max-allowed-qty"));
    if (newQty > max_allowed_qty) {
      this.raiseWarningForCustomCodition(ev, max_allowed_qty);
      return false;
    }
    if (newQty !== previousQty) {
      $input.val(newQty).trigger("change");
    }
    return false;
  },

  /**
   * It gose to recursive mode and calling same controller again and again when we added/change quantity with decimal in Cart line
   * @override
   */
  _changeCartQuantity: function (
    $input,
    value,
    $dom_optional,
    line_id,
    productIDs
  ) {
    $($dom_optional)
      .toArray()
      .forEach((elem) => {
        $(elem).find(".js_quantity").text(value);
        productIDs.push(
          $(elem).find("span[data-product-id]").data("product-id")
        );
      });
    $input.data("update_change", true);

    this.rpc("/shop/cart/update_json", {
      line_id: line_id,
      product_id: parseInt($input.data("product-id"), 10),
      set_qty: value,
      display: true,
    }).then((data) => {
      $input.data("update_change", false);
      // As we allowed user to order in decimal, we need to convert it to the Float instead of Integer
      // This way return values from controller and cart value will be same and no more change event trigger
      var check_value = parseFloat($input.val() || 0, 10);
      if (isNaN(check_value)) {
        check_value = 1;
      }
      if (value !== check_value) {
        $input.trigger("change");
        return;
      }
      if (!data.cart_quantity) {
        return (window.location = "/shop/cart");
      }
      $input.val(data.quantity);
      $(".js_quantity[data-line-id=" + line_id + "]")
        .val(data.quantity)
        .text(data.quantity);

      wSaleUtils.updateCartNavBar(data);
      wSaleUtils.showWarning(data.notification_info.warning);
      // Propagating the change to the express checkout forms
      Component.env.bus.trigger("cart_amount_changed", [
        data.amount,
        data.minor_amount,
      ]);
    });
  },

  /**
   * Change the Pricing fields HKD to USD on change of UOM in Product Page
   *
   * @param {JQuery} $parent
   * @private
   */
  _onChangeSelectedUom($parent, combination) {
    // USD Price will come from here: https://github.com/odoo-ps/pshk-artextile-international/blob/test2-17/website_artextile_jsi/models/product.py#L56
    var self = this;
    var $price_usd = $parent.find(".oe_price_usd .oe_currency_value");
    var $default_price_usd = $parent.find(
      ".oe_default_price_usd .oe_currency_value"
    );
    $price_usd.text(self._priceToStr(combination.price_usd));
    $default_price_usd.text(self._priceToStr(combination.list_price_usd));
  },

  /**
   * Override to update the USD price when UOM changed
   *
   */
  _onChangeCombination(ev, $parent, combination) {
    const result = this._super.apply(this, arguments);
    this._onChangeSelectedUom($parent, combination);
    return result;
  },

  _onClickCheckout: function (ev) {
    ev.preventDefault();
    ev.stopPropagation();

    const targetUrl = $(ev.currentTarget).attr("href");

    this.rpc("/shop/cart/split_order_check").then((result) => {
      if (result) {
        this.call("dialog", "add", ConfirmationDialog, {
          body: _t(
            "Reminder: Your cart has items with different delivery times. Confirm to wait for all items to ship together?"
          ),
          confirm: async () => {
            window.location.href = targetUrl;
          },
          cancel: () => {},
        });
      } else {
        window.location.href = targetUrl;
      }
    });
    return false;
  },

  _onProductReady: function () {
    const _super = this._super.bind(this);
    const ignoreReminderToday = cookie.get(IGNORE_REMINDER_TODAY_COOKIE);
    if (ignoreReminderToday) {
      return _super(...arguments);
    }

    this.rpc("/shop/cart/split_order_check", {
      product_id: this.rootProduct.product_id,
    }).then((result) => {
      if (result) {
        this.call("dialog", "add", DeliveryLeadTimeReminderDialog, {
          body: _t(
            "Reminder: This product has a different delivery time from item(s) in your cart. You can either check out your current cart first, or WAIT FOR all items to ship together. Confirm to add it now?"
          ),
          confirm: async () => {
            return _super(...arguments);
          },
          cancel: () => {},
        });
      } else {
        return _super(...arguments);
      }
    });
  },
});
