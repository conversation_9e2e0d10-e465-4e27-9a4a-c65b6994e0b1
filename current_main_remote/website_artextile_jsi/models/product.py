from odoo import fields, models, _


class ProductProduct(models.Model):
    _inherit = "product.product"

    def name_get(self):
        """Override to display custom name on website"""
        if not self._context.get("website_id"):
            return super(ProductProduct, self).name_get()
        return [(value.id, "%s" % (value.with_context(prefetch_fields=False).product_tmpl_id.display_name)) for value in self]

    def _price_compute(self, price_type, uom=None, currency=None, company=None, date=False):
        '''
            We display 2 price in product List and Product Page: `price` and `list_price`
            By default, we need to pass UOM = Yard
            `list_price` is computing here: https://github.com/odoo/odoo/blob/17.0/addons/website_sale/models/product_template.py#L463
        '''
        if not uom:
            uom = self.env.context.get('selected_uom')
        return super()._price_compute(price_type, uom=uom, currency=currency, company=company, date=date)


class ProductTemplate(models.Model):
    _inherit = "product.template"

    description = fields.Text()

    def get_uom(self):
        """Used in website for display related UOM"""
        secondary_uom_id = self.secondary_uom_id.id
        category_id = self.secondary_uom_id.category_id.id
        uoms = self.env['uom.uom'].search_read([('category_id', '=', category_id)], fields=["name"])
        # find the last sale order line and its uom
        partner_id = self.env.user.partner_id.id
        sale_line_data = self.env['sale.order.line'].search_read([
            ('order_partner_id', '=', partner_id),
            ('state', 'in', ['sent', 'sale', 'done']),
            ('product_uom.category_id', '=', category_id)
        ], ['product_uom'], order="id DESC", limit=1)
        if sale_line_data:
            secondary_uom_id = sale_line_data[0]['product_uom'][0]  # Get the first element of the tuple (id, name) returned by search_read
        return {'uom_list': uoms, 'default': secondary_uom_id}

    def _get_additionnal_combination_info(self, product_or_template, quantity, date, website):
        """OVERRIDE

            1) `selected_uom` will come from JS side when we change it in `Product Page`
            2) It will also come from `_get_additionnal_combination_info` controller when we click on any product in `Product Listing Page`

            We pass it with context and it will used in
            1) `_compute_price_rule` for `price`
            2) `_price_compute` for `list_price`
        """
        selected_uom = self._context.get('selected_uom') or self.env.ref("secondary_uom_jsi.product_uom_yard")
        combination_info = super()._get_additionnal_combination_info(product_or_template.with_context(selected_uom=selected_uom), quantity, date, website.with_context(selected_uom=self._context.get('selected_uom')))
        # Convert price, list_price from HKD to USD and display on product detail page
        currency_USD = self.env.ref('base.USD')
        combination_info['price_usd'] = self.env.company.currency_id._convert(
            from_amount=combination_info['price'],
            to_currency=currency_USD,
            company=self.env.company,
            date=fields.Date.context_today(self))
        combination_info['list_price_usd'] = self.env.company.currency_id._convert(combination_info['list_price'], currency_USD, self.env.company, fields.Date.context_today(self))
        return combination_info

    def _price_compute(self, price_type, uom=None, currency=None, company=None, date=False):
        '''
            We display 2 price in product List and Product Page: `price` and `list_price`
            By default, we need to pass UOM = Yard
            `list_price` is computing here: https://github.com/odoo/odoo/blob/17.0/addons/website_sale/models/product_template.py#L463
        '''
        if not uom:
            if selected_uom := self.env.context.get('selected_uom'):
                uom = self.env['uom.uom'].browse(int(selected_uom))
            else:
                # Default UOM = Yard everywhere in website
                uom = self.env.ref("secondary_uom_jsi.product_uom_yard")
        return super()._price_compute(price_type, uom=uom, currency=currency, company=company, date=date)
