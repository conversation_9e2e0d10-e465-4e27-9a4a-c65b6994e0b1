from odoo import models


class PriceList(models.Model):
    _inherit = "product.pricelist"

    def _get_product_price0000(self, product, *args, **kwargs):
        """Get the uom context and get the uom and add it to the fuction parameter if exist"""
        # if self._context.get('uom'):
        #     uom = self.env['uom.uom'].browse(int(self._context.get('uom')))
        #     kwargs = dict(kwargs, uom=uom)
        if self._context.get('selected_uom'):
            uom = self.env['uom.uom'].browse(int(self._context.get('selected_uom')))
            # kwargs = dict(kwargs, uom=uom)
            kwargs.update({'selected_uom': uom})
        return super()._get_product_price(product, *args, **kwargs)

    def _compute_price_rule(self, products, quantity, currency=None, uom=None, date=False, compute_price=True, **kwargs):
        """OVERRIDE
            Pass UOM to
            1) Product Listing page: updated contet in `_shop_lookup_products` in controller
            2) Product Page: Override to pass UOM as per selected
            For `price` : https://github.com/odoo/odoo/blob/17.0/addons/website_sale/models/product_template.py#L499
            from here: https://github.com/odoo/odoo/blob/17.0/addons/website_sale/models/product_template.py#L486

            Called from Product List
                This will used inside the _compute_price to compute `price`.  ref: https://github.com/odoo/odoo/blob/17.0/addons/product/models/product_pricelist_item.py#L379



        """
        if products.env.context.get('selected_uom'):
            uom = self.env['uom.uom'].browse(int(products.env.context.get('selected_uom')))
        else:
            # Default UOM = Yard everywhere in website
            uom = self.env.ref("secondary_uom_jsi.product_uom_yard")
        return super()._compute_price_rule(products, quantity, currency=currency, uom=uom, date=date, compute_price=compute_price, **kwargs)
