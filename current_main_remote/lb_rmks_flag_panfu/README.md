# LB Rmks Flag Plugin

## 概述

`lb_rmks_flag_panfu` 是一个 Odoo 17 插件，为客户（res.partner）添加了 "LB Side Flag" 字段，用于在销售订单行中自动添加备注前缀。

## 功能特性

### 1. 客户标记字段
- 在客户表单中添加 "LB Side Flag" 选择字段
- 可选值：
  - `正` (zheng)
  - `反` (fan)

### 2. 自动备注前缀
- 创建销售订单行时，根据客户的 LB 标记自动添加备注前缀
- `正` → 备注前缀：`正；`
- `反` → 备注前缀：`反；`

### 3. 手动应用功能
- 销售订单提供手动应用前缀的按钮
- 可批量更新订单中所有行的备注前缀

## 安装说明

1. 将插件文件夹放置在 Odoo 插件目录中
2. 重启 Odoo 服务
3. 在应用管理中搜索 "LB Rmks Flag"
4. 点击安装

## 使用方法

### 设置客户标记

1. 进入 **联系人** → 选择客户
2. 在客户表单中找到 **LB Side Flag** 字段
3. 选择 `正` 或 `反`
4. 保存客户信息

### 创建销售订单

1. 创建新的销售订单
2. 选择已设置 LB 标记的客户
3. 添加订单行时，系统会自动在备注字段添加对应前缀：
   - 客户标记为 `正` → 备注以 `正；` 开头
   - 客户标记为 `反` → 备注以 `反；` 开头

### 手动应用前缀

如果需要手动应用或重新应用前缀：

1. 在销售订单表单中
2. 点击 **Apply LB Rmks Prefix** 按钮
3. 系统会根据客户的当前标记更新所有订单行的备注前缀

## 技术实现

### 模型扩展

#### res.partner
```python
lb_rmks_flag = fields.Selection([
    ('zheng', '正'),
    ('fan', '反'),
], string='LB Side Flag', help='Label Side Flag: 正/反，用于销售订单明细自动备注')
```

#### sale.order
- 添加 `action_apply_lb_rmks_prefix()` 方法
- 批量更新订单行备注前缀

#### sale.order.line
- 重写 `create()` 方法，自动应用前缀
- 添加 `_apply_lb_rmks_prefix()` 私有方法

### 视图修改

- 在客户表单视图中添加 LB Side Flag 字段
- 字段位置：comment 字段之后

## 文件结构

```
lb_rmks_flag_panfu/
├── __init__.py
├── __manifest__.py
├── README.md
├── models/
│   ├── __init__.py
│   ├── partner.py          # 客户模型扩展
│   ├── sale_order.py       # 销售订单模型扩展
│   └── sale_order_line.py  # 销售订单行模型扩展
└── views/
    └── res_partner_view.xml # 客户视图扩展
```

## 日志记录

插件会记录以下操作日志：
- 销售订单行创建时的前缀应用
- 手动应用前缀的操作
- 前缀应用的详细信息

## 兼容性

- **Odoo 版本**: 17.0
- **依赖模块**: base, sale
- **数据库**: PostgreSQL

## 作者信息

- **作者**: panfu
- **版本**: 1.0.0
- **许可证**: LGPL-3

## 更新日志

### v1.0.0 (2025-08-19)
- 初始版本发布
- 实现客户 LB 标记字段
- 实现销售订单行自动前缀功能
- 添加手动应用前缀功能

## 支持

如有问题或建议，请联系开发者。

## 注意事项

1. 只有设置了 LB 标记的客户才会应用前缀
2. 前缀会添加到现有备注内容的开头
3. 如果备注已包含对应前缀，不会重复添加
4. 手动应用功能会覆盖现有前缀
