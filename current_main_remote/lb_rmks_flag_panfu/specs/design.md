# 技术方案设计

## 架构与技术选型
- 基于 Odoo 17，继承/扩展 res.partner（联系人）和 sale.order（销售订单）模型。
- 采用 Python 进行后端开发，XML 进行界面调整。

## 主要实现点

### 1. 联系人模型扩展
- 在 res.partner 增加新字段 `lb_rmks_flag`，类型为 Selection，选项为“正/反/空”。
- 字段在联系人表单界面可维护，默认空。

### 2. 销售订单自动填充逻辑
- 在 sale.order 选择/变更送货地址（partner_shipping_id）时，自动检测该联系人 `lb_rmks_flag` 字段。
- 若为“正”或“反”，则在 LB Rmks 字段（如为自定义字段，需确认字段名）最前方自动添加对应字样，原内容后移。
- 若为“空”，则不做处理。
- 若用户手动修改 LB Rmks 字段，允许编辑，但再次切换送货地址时仍按规则自动处理。

### 3. 界面调整
- 联系人表单视图增加 `lb_rmks_flag` 字段。
- 销售订单表单无需界面调整，仅需后端逻辑。

### 4. 测试策略
- 单元测试：覆盖联系人字段维护、销售订单切换送货地址、LB Rmks 字段自动填充等场景。
- 手工测试：实际录入订单、切换送货地址、打印Label等业务流程。

## 安全性
- 字段权限与 res.partner、sale.order 保持一致。

## 兼容性
- 仅影响销售订单录入及联系人维护，不影响其他模块。

```mermaid
graph TD
    A[联系人(res.partner)] -- 维护lb_rmks_flag --> B[销售订单(sale.order)]
    B -- 选择送货地址时自动填充 --> C[LB Rmks 字段]
``` 