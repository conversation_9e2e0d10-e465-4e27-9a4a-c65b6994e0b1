from odoo import models, api
import logging

_logger = logging.getLogger(__name__)

class SaleOrder(models.Model):
    _inherit = 'sale.order'

    def _get_lb_rmks_prefix(self):
        """获取 LB Rmks 前缀"""
        if self.partner_shipping_id:
            flag = self.partner_shipping_id.lb_rmks_flag
            _logger.info(f"Order {self.name} - shipping partner: {self.partner_shipping_id.name}, lb_rmks_flag: {flag}")
            if flag == 'zheng':
                return '正；'
            elif flag == 'fan':
                return '反；'
        else:
            _logger.info(f"Order {self.name} - no shipping partner set")
        return ''

    def _apply_lb_rmks_prefix_to_lines(self):
        """为所有订单行应用 LB Rmks 前缀"""
        prefix = self._get_lb_rmks_prefix()
        _logger.info(f"Applying prefix '{prefix}' to order {self.name} with {len(self.order_line)} lines")
        if prefix:
            for line in self.order_line:
                current_value = line.remarks or ''
                # 检查是否已经有前缀，避免重复添加
                if not current_value.startswith(('正；', '反；')):
                    line.remarks = f'{prefix}{current_value}'
                    _logger.info(f"Applied prefix to line {line.id}: {line.remarks}")
                else:
                    _logger.info(f"Line {line.id} already has prefix: {current_value}")
        else:
            _logger.info(f"No prefix to apply for order {self.name}")

    @api.onchange('partner_shipping_id')
    def _onchange_partner_shipping_id_lb_rmks_flag(self):
        """送货地址改变时，为所有订单行应用前缀"""
        self._apply_lb_rmks_prefix_to_lines()

    @api.onchange('order_line')
    def _onchange_order_line_lb_rmks_flag(self):
        """订单行改变时，为新增的订单行应用前缀"""
        self._apply_lb_rmks_prefix_to_lines()

    def action_apply_lb_rmks_prefix(self):
        """手动应用 LB Rmks 前缀的动作方法"""
        self._apply_lb_rmks_prefix_to_lines()
        return True


class SaleOrderLine(models.Model):
    _inherit = 'sale.order.line'

    @api.model_create_multi
    def create(self, vals_list):
        """创建订单行时自动应用 LB Rmks 前缀"""
        lines = super().create(vals_list)
        # 使用 flush 确保数据库状态一致
        self.env.flush_all()
        for line in lines:
            try:
                if line.order_id and line.order_id.partner_shipping_id:
                    prefix = line.order_id._get_lb_rmks_prefix()
                    _logger.info(f"Creating line for order {line.order_id.name}, shipping partner: {line.order_id.partner_shipping_id.name}, prefix: {prefix}")
                    if prefix:
                        current_value = line.remarks or ''
                        if not current_value.startswith(('正；', '反；')):
                            line.remarks = f'{prefix}{current_value}'
                            _logger.info(f"Applied prefix to line {line.id}: {line.remarks}")
                        else:
                            _logger.info(f"Line {line.id} already has prefix: {current_value}")
                    else:
                        _logger.info(f"No prefix needed for line {line.id}")
                else:
                    _logger.info(f"Line {line.id} - order_id: {line.order_id}, partner_shipping_id: {line.order_id.partner_shipping_id if line.order_id else 'N/A'}")
            except Exception as e:
                _logger.error(f"Error applying LB Rmks prefix to line {line.id}: {e}")
        return lines

    def write(self, vals):
        """更新订单行时检查是否需要应用 LB Rmks 前缀"""
        result = super().write(vals)
        # 如果更新了产品或其他相关字段，重新检查前缀
        if 'product_id' in vals or 'remarks' in vals:
            for line in self:
                try:
                    if line.order_id and line.order_id.partner_shipping_id:
                        prefix = line.order_id._get_lb_rmks_prefix()
                        if prefix:
                            current_value = line.remarks or ''
                            if not current_value.startswith(('正；', '反；')):
                                line.remarks = f'{prefix}{current_value}'
                                _logger.info(f"Applied prefix to updated line {line.id}: {line.remarks}")
                except Exception as e:
                    _logger.error(f"Error applying LB Rmks prefix to updated line {line.id}: {e}")
        return result