from odoo import http, _
from odoo.http import request
from odoo.addons.portal.controllers.portal import pager as portal_pager
from odoo.addons.sale.controllers.portal import CustomerPortal


class CustomerPortalSearchExtended(CustomerPortal):

    @http.route(['/my/orders', '/my/orders/page/<int:page>'], type='http', auth="user", website=True)
    def portal_my_orders(self, page=1, date_begin=None, date_end=None, sortby=None, **kw):
        """重写订单列表页面以支持客户采购单号和面料编号搜索"""
        return super().portal_my_orders(page=page, date_begin=date_begin, date_end=date_end, sortby=sortby, **kw)

    def _prepare_orders_domain(self, partner):
        # 先获取父类的基础域
        domain = super()._prepare_orders_domain(partner)
        search = request.params.get('search')
        customer_po = request.params.get('customer_po')
        internal_reference = request.params.get('internal_reference')

        # 处理标准搜索参数
        if search:
            # 搜索订单行中的客户采购单号和产品内部编号
            line_orders = request.env['sale.order.line'].search([
                '|',
                ('customer_po', 'ilike', search),
                ('product_id.default_code', 'ilike', search)
            ]).mapped('order_id').ids

            # 如果找到匹配的订单行，添加到搜索域中
            if line_orders:
                # 构建搜索域：订单名称 OR 客户采购单号/产品内部编号匹配的订单
                search_domain = [
                    '|',
                    ('name', 'ilike', search),
                    ('id', 'in', line_orders)
                ]
                # 合并基础域和搜索域
                domain = domain + search_domain
            else:
                # 如果没有找到匹配的订单行，只搜索订单名称
                domain = domain + [('name', 'ilike', search)]

        # 处理自定义搜索参数（兼容另一个插件的功能）
        if customer_po:
            line_orders = request.env['sale.order.line'].search([('customer_po', 'ilike', customer_po)]).mapped('order_id').ids
            if line_orders:
                domain += [('id', 'in', line_orders)]
            else:
                domain += [('id', '=', 0)]

        if internal_reference:
            line_orders = request.env['sale.order.line'].search([('product_id.default_code', 'ilike', internal_reference)]).mapped('order_id').ids
            if line_orders:
                domain += [('id', 'in', line_orders)]
            else:
                domain += [('id', '=', 0)]

        return domain

    def _prepare_quotations_domain(self, partner):
        # 先获取父类的基础域
        domain = super()._prepare_quotations_domain(partner)
        search = request.params.get('search')
        customer_po = request.params.get('customer_po')
        internal_reference = request.params.get('internal_reference')

        # 处理标准搜索参数
        if search:
            # 搜索订单行中的客户采购单号和产品内部编号
            line_orders = request.env['sale.order.line'].search([
                '|',
                ('customer_po', 'ilike', search),
                ('product_id.default_code', 'ilike', search)
            ]).mapped('order_id').ids

            # 如果找到匹配的订单行，添加到搜索域中
            if line_orders:
                # 构建搜索域：订单名称 OR 客户采购单号/产品内部编号匹配的订单
                search_domain = [
                    '|',
                    ('name', 'ilike', search),
                    ('id', 'in', line_orders)
                ]
                # 合并基础域和搜索域
                domain = domain + search_domain
            else:
                # 如果没有找到匹配的订单行，只搜索订单名称
                domain = domain + [('name', 'ilike', search)]

        # 处理自定义搜索参数（兼容另一个插件的功能）
        if customer_po:
            line_orders = request.env['sale.order.line'].search([('customer_po', 'ilike', customer_po)]).mapped('order_id').ids
            if line_orders:
                domain += [('id', 'in', line_orders)]
            else:
                domain += [('id', '=', 0)]

        if internal_reference:
            line_orders = request.env['sale.order.line'].search([('product_id.default_code', 'ilike', internal_reference)]).mapped('order_id').ids
            if line_orders:
                domain += [('id', 'in', line_orders)]
            else:
                domain += [('id', '=', 0)]

        return domain

    def _get_sale_searchbar_sortings(self):
        """Add custom fields to searchbar sorting options"""
        sortings = super()._get_sale_searchbar_sortings()
        sortings.update({
            'customer_po': {'label': _('Customer PO'), 'order': 'customer_po'},
            'internal_reference': {'label': _('Internal Reference'), 'order': 'internal_reference'},
        })
        return sortings

    def _prepare_sale_portal_rendering_values(
        self, page=1, date_begin=None, date_end=None, sortby=None,
        quotation_page=False, **kwargs
    ):
        values = super()._prepare_sale_portal_rendering_values(
            page, date_begin, date_end, sortby, quotation_page, **kwargs
        )

        # 聚合明细行 customer_po 和 internal_reference 用于显示
        orders = values.get('orders') or values.get('quotations')
        customer_po_agg_dict = {}
        internal_reference_agg_dict = {}
        if orders:
            for order in orders:
                po_list = order.order_line.mapped('customer_po')
                customer_po_agg_dict[order.id] = ', '.join([po for po in po_list if po])

                internal_ref_list = order.order_line.mapped('product_id.default_code')
                internal_reference_agg_dict[order.id] = ', '.join([ref for ref in internal_ref_list if ref])

        values['customer_po_agg_dict'] = customer_po_agg_dict
        values['internal_reference_agg_dict'] = internal_reference_agg_dict

        # 添加搜索参数到模板上下文
        values['search'] = request.params.get('search', '')

        return values