# Order Search by Customer PO and Internal Reference

This module extends the Odoo e-commerce portal to allow customers to search their orders using either Customer PO or Product Internal Reference.

## Features

1. Adds Customer PO field to sale order lines
2. Extends the portal search functionality to include Customer PO and Product Internal Reference
3. Displays Customer PO and Product Internal Reference in the portal order lists
4. Allows customers to search orders by Customer PO or Product Internal Reference

## Installation

1. Copy this module to your Odoo addons directory
2. Update the apps list in Odoo
3. Install the "Order Search by Customer PO and Internal Reference" module

## Configuration

No additional configuration is required. The module automatically extends the existing portal functionality.

## Usage

1. Customers can enter a Customer PO or Product Internal Reference in the search box on the "My Orders" or "Quotations" pages
2. The system will filter orders that match the entered value in either field
3. Customer PO can be entered during checkout or updated later in the order
4. Product Internal Reference is automatically available from the product configuration

## Technical Notes

- Extends the sale.order.line model with customer_po field
- Overrides portal controllers to include Customer PO and Product Internal Reference in search
- Searches Product Internal Reference through the product_id.default_code field
- Modifies portal templates to display the new fields