<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- 仅扩展明细行表格，显示 customer_po 字段 -->
    <record id="view_order_form_inherit_customer_po_internal_ref" model="ir.ui.view">
        <field name="name">sale.order.form.inherit.customer.po.internal.ref</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='order_line']/tree" position="inside">
                <field name="customer_po"/>
                <field name="product_id" invisible="1"/>
            </xpath>
        </field>
    </record>

    <!-- 可选：在 tree 视图中显示主表 customer_po 字段（如有业务需要） -->
    <!--
    <record id="view_order_tree_inherit_customer_po_fabric" model="ir.ui.view">
        <field name="name">sale.order.tree.inherit.customer.po.fabric</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='name']" position="after">
                <field name="customer_po" />
            </xpath>
        </field>
    </record>
    -->

    <!-- Add customer PO field to sale order tree view -->
    <record id="view_order_tree_inherit_customer_po_internal_ref" model="ir.ui.view">
        <field name="name">sale.order.tree.inherit.customer.po.internal.ref</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='name']" position="after">
                <field name="customer_po" />
            </xpath>
        </field>
    </record>

    <!-- Add customer PO field to sale order search view -->
    <record id="view_sales_order_filter_inherit_customer_po_internal_ref" model="ir.ui.view">
        <field name="name">sale.order.search.inherit.customer.po.internal.ref</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_sales_order_filter"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='name']" position="after">
                <field name="customer_po" />
            </xpath>
        </field>
    </record>
</odoo>